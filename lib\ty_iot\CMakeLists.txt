# Tuya IoT library CMakeLists.txt
# This library contains Tuya IoT SDK core functionality

set(COMPONENT_SRCS
    "src/atop_base.c"
    "src/atop_service.c"
    "src/file_download.c"
    "src/iotdns.c"
    "src/matop_service.c"
    "src/mqtt_bind.c"
    "src/mqtt_service.c"
    "src/tuya_ble_service.c"
    "src/tuya_endpoint.c"
    "src/tuya_iot.c"
    "src/tuya_ota.c"
    "src/tuya_wifi_provisioning.c"
)

set(COMPONENT_ADD_INCLUDEDIRS
    "../utils/include"
    "../coreHTTP/include"
    "../coreJSON/include"
    "../coreMQTT/include"
    "../middleware/include"
    "../esp32_port/include"
    "../../include"
)

set(COMPONENT_REQUIRES
    "utils"
    "coreHTTP"
    "coreJSON"
    "coreMQTT"
    "middleware"
    "esp32_port"
    "mbedtls"
)

idf_component_register(
    SRCS ${COMPONENT_SRCS}
    INCLUDE_DIRS ${COMPONENT_ADD_INCLUDEDIRS}
    REQUIRES ${COMPONENT_REQUIRES}
)
