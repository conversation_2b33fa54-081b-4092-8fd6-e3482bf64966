# ESP32 Port library CMakeLists.txt
# This library contains ESP32 platform specific implementations

set(COMPONENT_SRCS
    "src/ble_wrapper.c"
    "src/esp32_adc.c"
    "src/improv.cpp"
    "src/network_wrapper.c"
    "src/shipping.cpp"
    "src/storage_wrapper.c"
    "src/system_wrapper.c"
    "src/tuya_config_cli.c"
    "src/tuya_config_manager.c"
    "src/tuya_debug.c"
)

set(COMPONENT_ADD_INCLUDEDIRS
    "include"
    "../utils/include"
    "../../include"
)

set(COMPONENT_REQUIRES
    "utils"
    "mbedtls"
)

idf_component_register(
    SRCS ${COMPONENT_SRCS}
    INCLUDE_DIRS ${COMPONENT_ADD_INCLUDEDIRS}
    REQUIRES ${COMPONENT_REQUIRES}
)
