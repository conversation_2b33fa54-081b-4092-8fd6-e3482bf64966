#pragma once
// --- 故障码定义 ---
#define FAULT_LOW_BATTERY_BIT 0x01 // 位0：低电量 (0000 0001)
#define FAULT_LACK_WATER_BIT 0x04  // 位2：缺水 (0000 0100)
// 添加其他可能的故障：
// #define FAULT_MOTOR_BLOCKED_BIT  0x02 // 位1：电机堵塞 (0000 0010)
// #define FAULT_OVERHEAT_BIT       0x08 // 位3：过热 (0000 1000)

#ifdef __cplusplus
extern "C" {
#endif
void fault_init(void);
void set_fault(unsigned char fault_bit);
void clear_fault(unsigned char fault_bit);
unsigned char get_current_fault_code(void);
bool is_fault_active(unsigned char fault_bit);
#ifdef __cplusplus
}
#endif