/**
 * @file tuya_config.h
 * @brief IoT specific configuration file
 */
#pragma once

#ifndef TUYA_CONFIG_H_
#define TUYA_CONFIG_H_

#define SOFTWARE_VER     "0.8.0"
#define TUYA_NAMESPACE "tuya"   //提醒：不要顺便修改，要出问题，主要是因为命名空间和涂鸦Active数据key同名

#define SWITCH_PUMP     "1"
#define SWITCH_OZONE    "101"
#define COUNT_DOWN      "11"
#define BATTERY_LEVEL   "7"
#define FAULT           "4"

#define SWITCH_PUMP_ID     1
#define SWITCH_OZONE_ID    101
#define COUNT_DOWN_ID      11
#define BATTERY_LEVEL_ID   7
#define FAULT_ID           4

#if 0
#define TUYA_PRODUCT_KEY      "ff1lwoe4t5rkeg5m" // for test
#define TUYA_DEVICE_UUID      "tuyac0828c3dfbc6a170"
#define TUYA_DEVICE_AUTHKEY   "gxQPs3qtR0pNSx12hh0floVG117uUnJL"
#endif
#endif