#include <Arduino.h>
#include <stdio.h>
#include <stdlib.h>
#include <assert.h>
#include <WiFi.h>
#include "esp_pm.h"
#include "esp_sleep.h"
#include "tuya_log.h"
#include "tuya_config.h"
#include "tuya_iot.h"
#include "tuya_ota.h"
#include "cJSON.h"
#include "tuya_wifi_provisioning.h"
#include "improv.h"
#include "tuya_debug.h"
#include "tuya_config_manager.h"
#include "tuya_config_cli.h"
#include "storage_interface.h"
#include "tuya_config.h"
#include "shipping.h"
#include "device_control.h"
#include "fault.h"
#include "esp32_ota.h"

bool is_active_dfs_lock = false;
unsigned long last_activity_dfs_lock_time = 0;
const unsigned long DFS_LOCK_ACTIVITY_TIMEOUT = 5000; // 5秒无活动后释放锁
extern SemaphoreHandle_t fault_mutex;
extern esp_pm_lock_handle_t dfs_lock;
extern int count_down_time;
extern bool tuyaiot_connected;
extern char current_fault_code;
/* Tuya device handle */
tuya_iot_client_t client;
extern "C"
{
    void battery_task(void *pvParameters);
    void report_tuya(int dpid, int value, bool on);
    void dfs_power_management_init(int max_mhz, int min_mhz, bool light_sleep);
}

improv::ImprovComponent improvComponent;
void led_task(void *pvParameters);

void example_qrcode_print(const char *productkey, const char *uuid)
{
    TY_LOGI("https://smartapp.tuya.com/s/p?p=%s&uuid=%s&v=2.0", productkey, uuid);
    TY_LOGI("(Use this URL to generate a static QR code for the Tuya APP scan code binding)");
}

extern "C" void report_tuya(int dpid, int value, bool on)
{
    char report_value[128];
    bool unknow_dpid = false;

    if (!tuyaiot_connected) {
        TY_LOGW("Tuya IoT not connected, Not report DPID");
        return;
    }

    switch (dpid) {
        case SWITCH_PUMP_ID:
            if (on) {
                sprintf(report_value, "{\"%s\": true}", SWITCH_PUMP);
            } else {
                sprintf(report_value, "{\"%s\": false}", SWITCH_PUMP);
            }
            break;
        case SWITCH_OZONE_ID:
            if (on) {
                sprintf(report_value, "{\"%s\": true}", SWITCH_OZONE);
            } else {
                sprintf(report_value, "{\"%s\": false}", SWITCH_OZONE);
            }
            break;
        case BATTERY_LEVEL_ID:
            sprintf(report_value, "{\"%s\": %d}", BATTERY_LEVEL, value);
            break;
        case COUNT_DOWN_ID:
            sprintf(report_value, "{\"%s\": %d}", COUNT_DOWN, value);
            break;
        case FAULT_ID:
            sprintf(report_value, "{\"%s\": %d}", FAULT, value);
            break;
        default:
            TY_LOGE("Unsupported DP type");
            unknow_dpid = true;
            break;
    }

    if (!unknow_dpid) {
        tuya_iot_dp_report_json(&client, report_value);
        TY_LOGD("report_tuya: Data point upload value:%s", report_value);
    }
}

/* DP data reception processing function */
void tuya_iot_dp_download(tuya_iot_client_t *client, const char *json_dps)
{
    char *dp_id;
    cJSON *value_obj = NULL;
    bool has_unknown_dpid = false;

    TY_LOGD("Data point download value:%s", json_dps);

    /* Parsing json string to cJSON object */
    cJSON *dps = cJSON_Parse(json_dps);
    if (dps == NULL)
    {
        TY_LOGE("JSON parsing error, exit!");
        return;
    }

    /* 遍历JSON对象中的所有键值对 */
    cJSON *current_item = dps->child;
    while (current_item != NULL) {
        dp_id = current_item->string;  // 获取键名字符串
        switch (atoi(dp_id))
        {
            case SWITCH_PUMP_ID:
                value_obj = cJSON_GetObjectItem(dps, SWITCH_PUMP);
                if (value_obj != NULL)
                    handle_button6_click();
                break;
            case SWITCH_OZONE_ID:
                value_obj = cJSON_GetObjectItem(dps, SWITCH_OZONE);
                if (value_obj != NULL)
                    handle_button7_click();
                break;
            case COUNT_DOWN_ID:
                value_obj = cJSON_GetObjectItem(dps, COUNT_DOWN);
                if (value_obj != NULL)
                    count_down_time = value_obj->valueint;
                break;
            default:
                has_unknown_dpid = true;
                TY_LOGW("Unsupported DP type");
                break;
        }

        /* 移动到下一个键值对 */
        current_item = current_item->next;
    }

    /* relese cJSON DPS object */
    cJSON_Delete(dps);
#if 0
    /* Report the received data to synchronize the status. */
    if (!has_unknown_dpid) {
        tuya_iot_dp_report_json(client, json_dps);
        TY_LOGW("tuya_iot_dp_download: Data point upload value:%s", json_dps);
    }
#endif
}

/* Tuya OTA event callback */
void user_upgrade_notify_on(tuya_iot_client_t *client, cJSON *upgrade)
{
    TY_LOGI("----- Upgrade information -----");
    TY_LOGI("OTA Channel: %d", cJSON_GetObjectItem(upgrade, "type")->valueint);
    TY_LOGI("Version: %s", cJSON_GetObjectItem(upgrade, "version")->valuestring);
    TY_LOGI("Size: %s", cJSON_GetObjectItem(upgrade, "size")->valuestring);
    TY_LOGI("MD5: %s", cJSON_GetObjectItem(upgrade, "md5")->valuestring);
    TY_LOGI("HMAC: %s", cJSON_GetObjectItem(upgrade, "hmac")->valuestring);
    TY_LOGI("URL: %s", cJSON_GetObjectItem(upgrade, "url")->valuestring);
    TY_LOGI("HTTPS URL: %s", cJSON_GetObjectItem(upgrade, "httpsUrl")->valuestring);

    tuya_ota_begin(&ota_handle, upgrade);
}

/* Tuya SDK event callback */
static void user_event_handler_on(tuya_iot_client_t *client, tuya_event_msg_t *event)
{
    TY_LOGD("Tuya Event ID:%d(%s)", event->id, EVENT_ID2STR(event->id));
    switch (event->id)
    {
    case TUYA_EVENT_BIND_START:
        TY_LOGI("Device START BIND!");
        /* Print the QRCode for Tuya APP bind */
        device_control_tuya_iot_status(false);
        break;

    case TUYA_EVENT_WIFI_CONNECTED:
        TY_LOGI("WiFi Connected! SSID: %s", WiFi.SSID().c_str());
        device_control_wifi_connected_flash(true);
        break;

    case TUYA_EVENT_WIFI_DISCONNECT:
        TY_LOGI("WiFi Disconnected!");
        device_control_wifi_connected_flash(false);
        break;

    case TUYA_EVENT_MQTT_CONNECTED:
        TY_LOGI("Device MQTT Connected!");
        device_control_tuya_iot_status(true);
        report_tuya(COUNT_DOWN_ID, count_down_time, true);  // 报告倒计时时间
        report_tuya(SWITCH_PUMP_ID, 0, false);  // 启动时同步APP状态
        report_tuya(SWITCH_OZONE_ID, 0, false); // 启动时同步APP状态
        report_tuya(FAULT_ID, current_fault_code, false);
        break;

    case TUYA_EVENT_MQTT_DISCONNECT:
        TY_LOGI("Device MQTT Disconnected!");
        device_control_tuya_iot_status(false);
        break;

    case TUYA_EVENT_DP_RECEIVE:
        tuya_iot_dp_download(client, (const char *)event->value.asString);
        break;

    case TUYA_EVENT_UPGRADE_NOTIFY:
        user_upgrade_notify_on(client, event->value.asJSON);
        break;

    case TUYA_EVENT_TIMESTAMP_SYNC:
        TY_LOGI("Sync timestamp:%d", event->value.asInteger);
        break;

    default:
        break;
    }
}

extern "C"
{
    bool isWifiConnected()
    {
        return (WiFi.status() == WL_CONNECTED);
    }
}

void wifi_info_cb(wifi_info_t wifi_info)
{
    TY_LOGI("wifi ssid: %s", wifi_info.ssid);
    TY_LOGI("wifi pwd: %s", wifi_info.pwd);
    if (!improvComponent.isWifiConnected())
    {
        improvComponent.wifi_connect(
            std::string(reinterpret_cast<const char *>(wifi_info.ssid)),
            std::string(reinterpret_cast<const char *>(wifi_info.pwd)));
    }
}

void device_control_task(void *pvParameters)
{
    int count = 0;
    UBaseType_t stackLeft;

    while (1) {
        device_control_loop();
        vTaskDelay(pdMS_TO_TICKS(25));
#if 0
        // 每10秒（10000ms / 25ms）打印一次堆栈剩余空间
        if (++count >= 400) {
            stackLeft = uxTaskGetStackHighWaterMark(NULL);
            TY_LOGD("[device_control_task] Stack high water mark: %d", stackLeft);
            count = 0;
        }
#endif
    }
}

void setup()
{
    int ret = OPRT_OK;
    bool deepSleepNormalStart = false;

    pinMode(3, INPUT);  //电机采样
    pinMode(4, INPUT);  //电池采样
    pinMode(0, OUTPUT);
    digitalWrite(0, LOW);  //关断水泵和臭氧总电源

    Serial.begin(115200);
    /* 初始化 Tuya 设备及调试输出等级 */
    log_set_level(LOG_INFO);
    TY_LOGI("Starting ESP32 Tuya IoT Device...");
    // 初始化存储系统
    TY_LOGI("Initializing storage...");
    ret = local_storage_init(TUYA_NAMESPACE);
    if (ret != OPRT_OK)
    {
        Serial.printf("Storage init failed: %d\n", ret);
        ESP.restart();
        return;
    }

    // 检查deep sleep唤醒原因
    esp_sleep_wakeup_cause_t wakeup_reason = esp_sleep_get_wakeup_cause();

    switch(wakeup_reason) {
        case ESP_SLEEP_WAKEUP_GPIO:
            TY_LOGI("Wakeup from deep sleep by GPIO");

            // 使用OneButton库检查深度睡眠唤醒
            if (check_deep_sleep_wakeup()) {
                TY_LOGI("Deep sleep wakeup confirmed - clearing shipping mode");
                deepSleepNormalStart = true;
            } else {
                TY_LOGI("Deep sleep wakeup rejected - returning to deep sleep");
                deep_sleep();
            }
            break;

        case ESP_SLEEP_WAKEUP_UNDEFINED:
        default:
            TY_LOGI("Normal startup (not from sleep)");
            break;
    }

    if (should_enter_shipping_mode())
    {
        TY_LOGI("Entering shipping mode, sleeping...");
#if 1
        light_sleep();
#else
        if (!deepSleepNormalStart)        
            deep_sleep();
#endif
    }

    dfs_power_management_init(80, 40, true); // 初始化电源管理
    fault_init(); // 初始化故障检测系统
    // 创建 LED 控制任务，增加堆栈大小以策安全（无论按钮状态如何都要创建）
    xTaskCreate(led_task, "led_task", 3072, NULL, 9, NULL);
    // Initialize device control
    device_control_init();
    // Create device control task
    xTaskCreate(device_control_task, "device_control_task", 4096, NULL, 5, NULL);
    // 创建 battery 控制任务，增加堆栈大小以策安全
    xTaskCreate(battery_task, "battery_task", 3072, NULL, 1, NULL);

    xTaskHandle loopTaskHandle = xTaskGetHandle("loopTask"); // 获取 loopTask 的句柄
    if (loopTaskHandle != NULL) {
      vTaskPrioritySet(loopTaskHandle, 7); // 设置为高优先级
      TY_LOGI("Loop task priority increased.");
    }
    else
      TY_LOGE("Failed to get loop task handle.");

    // 加载Tuya配置（优先从NVS读取，没有则使用默认值）
    TY_LOGI("Loading Tuya configuration...");
    ret = tuya_config_load();
    if (ret != OPRT_OK)
    {
        TY_LOGW("Config load failed, using defaults: %d", ret);
    }

    // 打印当前配置
    tuya_config_print_current();

    // 初始化配置CLI
    tuya_config_cli_init();

    // 初始化WiFi (但不连接)
    WiFi.mode(WIFI_STA);
    vTaskDelay(pdMS_TO_TICKS(100));

    // 初始化 Improv 组件
    improvComponent.setup();
    vTaskDelay(pdMS_TO_TICKS(100));

    /* 获取动态配置（优先从NVS读取） */
    const tuya_config_data_t *dynamic_config = tuya_config_get();

    /* 定义 Tuya 设备配置 */
    const tuya_iot_config_t config = {
        .productkey = dynamic_config->product_key,
        .uuid = dynamic_config->device_uuid,
        .authkey = dynamic_config->device_authkey,
        .software_ver = SOFTWARE_VER,
        .storage_namespace = TUYA_NAMESPACE,
        .event_handler = user_event_handler_on,
        .network_check = isWifiConnected};

    /* 执行完整的调试分析 */
    tuya_debug_full_analysis(&config);

    TY_LOGI("Initializing Tuya IoT...");
    ret = tuya_iot_init(&client, &config);
    if (ret != OPRT_OK)
    {
        Serial.printf("Tuya IoT init failed: %d\n", ret);
        ESP.restart();
        return;
    }

    tuya_ota_config_t ota_config = {
        .client = &client,
        .event_cb = user_ota_event_cb,
        .range_size = 8192,
        .timeout_ms = 5000
    };
    tuya_ota_init(&ota_handle, &ota_config);

    TY_LOGI("Starting Tuya IoT...");
    /* 启动 Tuya IoT 任务 */
    tuya_iot_start(&client);

    TY_LOGI("Starting WiFi provisioning...");
    /* Start wifi provisioning, ble get wifi provisioning params */
    tuya_wifi_provisioning(&client, WIFI_PROVISIONING_MODE_BLE, &wifi_info_cb);
}

void loop()
{
    /* Loop to receive packets, and handles client keepalive */
    tuya_iot_yield(&client);
    vTaskDelay(pdMS_TO_TICKS(25));
    // 超时，检查是否需要释放锁
    xSemaphoreTake(fault_mutex, portMAX_DELAY);
    if (is_active_dfs_lock && (millis() - last_activity_dfs_lock_time > DFS_LOCK_ACTIVITY_TIMEOUT))
    {
        esp_pm_lock_release(dfs_lock); // 超时无活动，释放锁
        is_active_dfs_lock = false;
        TY_LOGD("UART inactive for %d ms, released power lock", DFS_LOCK_ACTIVITY_TIMEOUT);
    }
    xSemaphoreGive(fault_mutex);
}
