#include <math.h>
#include "tuya_log.h"
#include "esp32_adc.h"
#include "tuya_config.h"
#include "fault.h"
#include "device_control.h"

extern bool pump_dry_run_flag;
extern unsigned char current_fault_code;
extern adc_config_t adc_config_vbat;
extern void report_tuya(int dpid, int value, bool on);

// 定义一个结构体来存储电压和SOC的对应关系
typedef struct {
    float voltage;
    float soc;
} SocPoint;
// 电池OCV-SOC查找表 (通用三元锂电池数据，基于25°C左右)
// 注意：0% SOC对应比克H18650CQ的放电截止电压2.75V
// 100% SOC对应充电截止电压4.20V
// 中间数据点从通用曲线调整而来，实际应用中应进行精确标定
const SocPoint ocv_soc_table[] = {
    {2.75f, 0.0f},   // 0% SOC (调整为比克H18650CQ的截止电压)
    {3.00f, 10.0f},
    {3.20f, 20.0f},
    {3.40f, 30.0f},
    {3.50f, 40.0f},
    {3.60f, 50.0f},
    {3.70f, 60.0f},
    {3.80f, 70.0f},
    {3.90f, 80.0f},
    {4.00f, 90.0f},
    {4.20f, 100.0f}  // 100% SOC (比克H18650CQ的充电电压)
};
const int OCV_SOC_TABLE_SIZE = sizeof(ocv_soc_table) / sizeof(SocPoint);
extern bool is_pump_zone_run();
// 根据电压和温度估算SOC百分比
// 注意：此函数使用了单OCV-SOC查找表，未进行复杂的温度补偿
// 实际应用中，更精确的温度补偿需要多条温度-OCV-SOC曲线或复杂模型
float get_soc_from_voltage_and_temp(float voltage, float temperature_celsius) {
    // 处理边界条件
    if (voltage >= ocv_soc_table[OCV_SOC_TABLE_SIZE - 1].voltage) {
        return 100.0f;
    }
    if (voltage <= ocv_soc_table[0].voltage) {
        return 0.0f;
    }

    // 查找并进行线性插值
    for (int i = 0; i < OCV_SOC_TABLE_SIZE - 1; i++) {
        // 确保电压在当前段的范围内
        if (voltage >= ocv_soc_table[i].voltage && voltage < ocv_soc_table[i+1].voltage) {
            float v1 = ocv_soc_table[i].voltage;
            float soc1 = ocv_soc_table[i].soc;
            float v2 = ocv_soc_table[i+1].voltage;
            float soc2 = ocv_soc_table[i+1].soc;

            // 线性插值
            float soc = soc1 + (voltage - v1) * (soc2 - soc1) / (v2 - v1);
            return soc;
        }
    }
    return 0.0f; // 理论上不会到达这里，除非电压超出范围
}

float update_battery_soc()
{
    float voltage = ReadVoltageSingle(&adc_config_vbat);
    float temp = temperatureRead();
    float battery_soc = get_soc_from_voltage_and_temp(voltage, temp);
    TY_LOGD("Battery Voltage: %.2fV, Temperature: %.2f°C, SOC: %.1f%%\n", voltage, temp, battery_soc);
    return battery_soc;
}

void battery_task(void *pvParameters)
{
    int count = 0;
    UBaseType_t stackLeft;
    float battery_soc, previous_battery_soc;

    previous_battery_soc = update_battery_soc();
    report_tuya(BATTERY_LEVEL_ID, (int)previous_battery_soc, false);

    while (1)
    {
        if (is_pump_ozone_running()) {  // 如果水泵正在运行，则不更新SOC
            vTaskDelay(pdMS_TO_TICKS(5000));    //如果没有这行将导致涂鸦协议栈无法运行导致上报数据失败
            continue;
        }
        battery_soc = update_battery_soc();
        if (fabs(battery_soc - previous_battery_soc) > 9.5) {
            report_tuya(BATTERY_LEVEL_ID, (int)battery_soc, false);
            previous_battery_soc = battery_soc;
        }

        if (battery_soc <= 30) {
            if (!is_fault_active(FAULT_LOW_BATTERY_BIT)) {
                set_fault(FAULT_LOW_BATTERY_BIT);
                report_tuya(FAULT_ID, current_fault_code, false);
            }
        } else {
            if (is_fault_active(FAULT_LOW_BATTERY_BIT)) {
                clear_fault(FAULT_LOW_BATTERY_BIT);
                report_tuya(FAULT_ID, current_fault_code, false);
            }
        }
        vTaskDelay(pdMS_TO_TICKS(10000));

#if 0
        // 每约10秒打印一次堆栈剩余空间
        if (++count >= 1)
        {
            stackLeft = uxTaskGetStackHighWaterMark(NULL);
            TY_LOGD("[battery_task] Stack high water mark: %d", stackLeft);
            count = 0;
        }
#endif
    }
}


