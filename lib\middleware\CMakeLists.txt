# Middleware library CMakeLists.txt
# This library contains transport layer wrappers for HTTP and MQTT

set(COMPONENT_SRCS
    "src/http_client_wrapper.c"
    "src/mqtt_client_wrapper.c"
)

set(COMPONENT_ADD_INCLUDEDIRS
    "include"
    "../utils/include"
)

set(COMPONENT_REQUIRES
    "utils"
    "mbedtls"
)

idf_component_register(
    SRCS ${COMPONENT_SRCS}
    INCLUDE_DIRS ${COMPONENT_ADD_INCLUDEDIRS}
    REQUIRES ${COMPONENT_REQUIRES}
)
